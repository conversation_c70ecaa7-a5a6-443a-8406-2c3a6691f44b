using System.Collections.Generic;
using System.Linq;
using com.luxza.granlog;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class RefereeShangHai : BaseReferee<MatchShangHai, MatchScorer<PERSON><PERSON><PERSON><PERSON>, GameEventPublisher>
    {
        internal RefereeShangHai(MatchShangHai match) 
            : base(match, new MatchScorerShangHai(match))
        {
        }

        public override bool IsReachGameEnd => Match.IsReachEndRound;

        public override bool IsNextRoundOverMaxRound { get; }

        public override void AcceptHit(Segment segment, System.Numerics.Vector2? hitPosition)
        {
            if (IsReachGameEnd)
            {
                Log.d("Receive key. but already finished!");
                return;
            }

            var currentRoundNo = CurrentRoundAtCurrentTeam.No;

            // 检查是否打中了当前轮次对应的目标
            bool isHit = IsValidHitForCurrentRound(segment, currentRoundNo);

            // 确定虚拟命中区域：如果命中目标则为实际区域，否则为Miss
            Segment virtualHit = isHit ? segment : Segment.Miss;

            // 记录投掷（无论是否命中都要记录）
            Match.StoreHit(_currentThrowingUnitIndex, segment, virtualHit, hitPosition);
            
            if (isHit)
            {
                // Determine the segment value (1 for single, 2 for double, 3 for triple)
                int segmentValue = 0;
                if (segment.IsSingle) segmentValue = 1;
                else if (segment.IsDouble) segmentValue = 2;
                else if (segment.IsTriple) segmentValue = 3;

                // Record the hit for Shanghai scoring
                Scorer.RecordHit(CurrentUnit.Id, CurrentUnit.CurrentThrower.GranId, segmentValue, currentRoundNo - 1, currentRoundNo);
                EventPublisher?.PublishUpdateProgress(CurrentUnit);
                // Check if Shanghai was achieved
                if (Scorer.IsShanghaiAchieved(CurrentUnit.Id))
                {
                    // 检查游戏模式
                    if (!Match.Rule.IsPlayUntilMaxRound)
                    {
                        // 传统模式：达成Shanghai即结束游戏
                        GameEnd();
                        return;
                    }
                    else
                    {
                        // 新模式：达成Shanghai但继续游戏
                        Log.d($"Round {currentRoundNo}: Shanghai achieved for area {currentRoundNo}, continuing game");
                    }
                }
            }
            else
            {
                // 没有打中目标，记录为Miss，但仍需要记录0分以便回退功能正常工作
                Scorer.RecordHit(CurrentUnit.Id, CurrentUnit.CurrentThrower.GranId, 0, currentRoundNo - 1, currentRoundNo);
                Log.d($"Round {currentRoundNo}: Hit {segment.Code} but expected target for round {currentRoundNo}");
                EventPublisher?.PublishUpdateProgress(CurrentUnit);
            }
            
            // Move to next turn
            if (Match.IsReachEndOfRound(_currentThrowingUnitIndex))
            {
                // 检查当前回合是否有miss镖，如果有且halfRound设置为1，则分数减半
                var isHalf = CheckAndApplyHalfRoundPenalty();
                Log.d($"isHalf: {isHalf}");
                if (isHalf)
                {
                    EventPublisher?.PublishUpdateProgress(CurrentUnit);
                }
                EventPublisher?.PublishEndTurn(CurrentUnit);
            }

            // Check if game should end after this round
            if (Match.IsReachEndRound)
            {
                GameEnd();
            }
        }

        /// <summary>
        /// 检查当前投掷是否命中了当前轮次的有效目标
        /// </summary>
        /// <param name="segment">投掷命中的区域</param>
        /// <param name="roundNo">当前轮次号</param>
        /// <returns>是否为有效命中</returns>
        private bool IsValidHitForCurrentRound(Segment segment, int roundNo)
        {
            if (roundNo < 1 || roundNo > 20)
            {
                return false;
            }

            // 检查是否命中了当前轮次对应的数字
            // 例如：第1轮只能命中1的区域（S1_In, D1, T1）
            // 第2轮只能命中2的区域（S2_In, D2, T2），以此类推

            if (segment.PositionCode == roundNo)
            {
                // 检查是否为有效的命中类型（Single, Double, Triple）
                return segment.IsSingle || segment.IsDouble || segment.IsTriple;
            }

            return false;
        }

        /// <summary>
        /// 检查当前回合是否有miss镖，如果有且halfRound设置为1，则分数减半
        /// </summary>
        private bool CheckAndApplyHalfRoundPenalty()
        {
            var currentRoundNo = CurrentRoundAtCurrentTeam.No;

            // 检查当前轮次是否启用了halfRound规则（索引从0开始，所以减1）
            if (currentRoundNo > 0 && currentRoundNo <= Match.Rule.HalfRoundOption.Length &&
                Match.Rule.HalfRoundOption[currentRoundNo - 1] == 1)
            {
                // 检查当前回合是否有miss镖
                if (HasMissInCurrentRound())
                {
                    // 分数减半
                    Scorer.HalfScore(CurrentUnit.Id, currentRoundNo);
                    Log.d($"Round {currentRoundNo}: Half score penalty applied due to miss dart");
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 检查当前回合是否有miss镖
        /// </summary>
        /// <returns>如果当前回合有miss镖返回true</returns>
        private bool HasMissInCurrentRound()
        {
            var currentRound = CurrentRoundAtCurrentTeam;
            if (currentRound.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                // 检查当前回合的所有投掷是否有miss
                return segmentInput.Throws.Any(t => !t.IsEmpty && t.IsMiss);
            }
            return false;
        }
        
        public override void ChangeToNextTeam()
        {
            if (!Match.IsReachEndOfRound(_currentThrowingUnitIndex))
            {
                EventPublisher?.PublishEndTurn(CurrentUnit);
            }
            EndRoundAtCurrentTeam();
            FixRoundAtCurrentUnit();
            if (IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
            else
            {
                var sender = CurrentUnit;
                _currentThrowingUnitIndex = NextTeamIndex(_currentThrowingUnitIndex);
                StartRoundAtCurrentTeam();
                EventPublisher.PublishChange(sender);
            }
        }
        
        protected virtual int NextTeamIndex(int currentTeamIndex)
        {
            return (currentTeamIndex + 1 >= Match.ParticipantTeams.Count) ? 0 : currentTeamIndex + 1;
        }

        public override int TotalScoreAtCurrentThrowingTeam { get; }

        public override int CurrentScore(string unitId)
        {
            return Scorer.GetTotalScore(unitId);
        }

        public override void RefreshGameData()
        {
            
        }

        protected override void GameEnd()
        {
            MatchFinishStatus matchFinishStatus = CalculateRanking();
            EventPublisher.PublishFinishMatch(matchFinishStatus);
        }

        protected override MatchFinishStatus CalculateRanking()
        {
            // 新模式：按总分数排名（分数高到低）
            if (Participants.Count == 1) return new MatchFinishStatus(Participants.AllUnits[0]);

            var ordered = Participants.AllUnits.OrderByDescending(team =>
            {
                // 按总分数降序排列
                return Scorer.TotalScore(team.Id);
            }).ThenByDescending(team =>
            {
                // 如果总分相同，按Shanghai区域数量排序
                return Scorer.GetShanghaiAreas(team.Id).Count;
            }).ToList();

            // 设置排名
            int rank = 1;
            for (int i = 0; i < ordered.Count; i++)
            {
                if (i == 0)
                {
                    Participants.Unit(ordered[i].Id).GameRanking = rank;
                    continue;
                }

                var previousTeamId = ordered[i - 1].Id;
                var currentTeamId = ordered[i].Id;

                // 比较总分数和Shanghai区域数
                if (Scorer.TotalScore(previousTeamId) == Scorer.TotalScore(currentTeamId) &&
                    Scorer.GetShanghaiAreas(previousTeamId).Count == Scorer.GetShanghaiAreas(currentTeamId).Count)
                {
                    Participants.Unit(currentTeamId).GameRanking = rank;
                    continue;
                }

                Participants.Unit(currentTeamId).GameRanking = ++rank;
            }

            var winner = IsDrawGame() ?
                Participants.AllUnits.First(team => team.GameRanking == 1) :
                null;

            return new MatchFinishStatus(winner);
        }

        public override IEnumerable<(Round round, int score)> RoundsAndScoresAt(string unitId)
        {
            return AllRoundsAt(unitId).Select(r => (r, Scorer.TotalScoreInRound(r)));
        }

        public override Award[] AchievableAward => GameRuleShangHai.AchievableAwards;

        /// <summary>
        /// 回退当前玩家的最后一镖
        /// </summary>
        /// <returns>是否成功回退</returns>
        private bool RevertLastThrow()
        {
            var currentPlayer = CurrentUnit.CurrentThrower;
            bool success = Scorer.RevertLastThrow(CurrentUnit.Id, currentPlayer.GranId);

            if (success)
            {
                EventPublisher?.PublishUpdateProgress(CurrentUnit);
                Log.d($"Reverted last throw for player {currentPlayer.GranId}");
            }

            return success;
        }

        /// <summary>
        /// 回退当前玩家的整个轮次
        /// </summary>
        /// <param name="roundIndex">轮次索引（0-based）</param>
        /// <returns>是否成功回退</returns>
        private bool RevertRound(int roundIndex)
        {
            var currentPlayer = CurrentUnit.CurrentThrower;
            bool success = Scorer.RevertRound(CurrentUnit.Id, currentPlayer.GranId, roundIndex - 1);

            if (success)
            {
                EventPublisher?.PublishUpdateProgress(CurrentUnit);
                Log.d($"Reverted round {roundIndex + 1} for player {currentPlayer.GranId}");
            }

            return success;
        }

        protected override void ResetRoundData()
        {
            var currentRoundNo = CurrentRoundAtCurrentTeam.No;

            // 回退该轮次产生的减半惩罚（如果有的话）
            if (Scorer.RevertHalfPenaltyForRound(CurrentUnit.Id, currentRoundNo))
            {
                Log.d($"Round {currentRoundNo}: Reverted half penalty when resetting round data");
            }

            RevertRound(currentRoundNo);
        }

        protected override void ResetThrowData()
        {
            RevertLastThrow();
        }

        /// <summary>
        /// 重新开始游戏
        /// </summary>
        public override void ReStartGame()
        {
            Scorer.ReStartGame();
            base.ReStartGame();
        }
    }
}
